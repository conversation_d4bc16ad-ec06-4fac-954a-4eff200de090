'use strict';

const co 	= require('co');
const moment= require('moment');

const PROFILE_SCHEMA    = require('../../validation-schemas/official').profile;
const ProfileSanctioningService = require('./official-sanctioning/_OfficialProfileSanctioningService');
const SportEngineUtils  = require('../../lib/SEUtilsService');
const AAUUtils  = require('../../lib/AAUUtilsService');
const {USAV_SANCTIONING_TYPE}  = require('../../constants/common');

const PROFILE_FIELDS = [
	'is_official', 'is_staff', 'advancement', 'country', 'region', 'usav_num', 'rank', 'address', 
    'city', 'state', 'zip', 'user_id',
    'security_pin', 'webpoint_data', 'background_screening', 'aau_region', 'aau_number',
    'special_sizing_requests', 'birthdate', 'emergency_contact_name', 'emergency_phone', 'profile_completed_at',
    'emergency_contact_relationship', 'safesport_end_date', 'safesport_start_date', 'safesport_statusid',
    'webpoint_modified', 'bg_expire_date', 'mbr_expire_date', 'arbiter_pay_username', 'arbiter_pay_account_number',
    'rq_pay_username'
];

const PROFILE_FIELDS_FOR_UPDATE = [
    'is_official', 'is_staff', 'region', 'usav_num', 'address', 'country', 'city', 'state', 'zip', 'advancement',
    'is_staff_only', 'rank', 'aau_region', 'aau_number', 'special_sizing_requests', 'not_completed', 'aau_not_completed',
    'birthdate', 'emergency_contact_name', 'emergency_phone', 'emergency_contact_relationship', 'arbiter_pay_username',
    'arbiter_pay_account_number', 'rq_pay_username'
];

const INSERT_CLOTHES_FAILED_REASON = 'INSERT_CLOTHES_FAILED';

class ProfileService {
    constructor (ProfileSanctioningService, SportEngineUtils, AAUUtils) {
        this.ProfileSanctioningService = new ProfileSanctioningService(this, SportEngineUtils, AAUUtils);
    }

	get MIN_PIN_VALUE () {
		return 10000;
	}

	get MAX_PIN_VALUE () {
		return 99999;
	}

	get PIN_GENERATION_ATTEMPTS () {
		return 10;
	}

	get PROFILE_FIELDS () {
		return PROFILE_FIELDS;
	}

	get UNIQUE_VIOLATION_ERR_CODE () {
		return '23505';
	}

	get SECURITY_PIN_CONSTRAINT_NAME () {
		return 'unique_official_security_pin';
	}

	get MIN_ATTEMPT_VALUE () {
		return 1;
	}

	get PROFILE_FIELDS_FOR_UPDATE () {
	    return PROFILE_FIELDS_FOR_UPDATE;
    }

	// covered 😄👍
	_validate (data) {
		let res = PROFILE_SCHEMA.validate(data);

		if (res.error) {
			throw { validationErrors: res.error.details.map(err => ({ message: err.message, path: err.path })) };
		}

		return res.value;
	}
	// covered 😄👍
	_insertProfileRow (attempt, rowData) {
		if (attempt < this.MIN_ATTEMPT_VALUE) {
			return Promise.reject(new Error(`Attempt should be greater or equal ${this.MIN_ATTEMPT_VALUE}`));
		}

		rowData.security_pin = this.genSecurityPin();

		// 💬 Should we move this to SQLGenerator
		let selectSubQuery = squel.select();

		for (let _field of this.PROFILE_FIELDS) {
			// ❗️ https://github.com/hiddentao/squel/issues/264
            selectSubQuery.field(squel.str('?', rowData[_field]));
		}	

		selectSubQuery.where('NOT EXISTS (SELECT * FROM "official" WHERE "user_id" = ?)', rowData.user_id);

		let query = squel.insert().into('official')
			.fromQuery(this.PROFILE_FIELDS, selectSubQuery)
			.returning('official_id');

		return Db.query(query)
		.then(result => result.rows[0] || null)
		.catch(err => {
			if (err.code === this.UNIQUE_VIOLATION_ERR_CODE && err.constraint === this.SECURITY_PIN_CONSTRAINT_NAME) {
				if (attempt < this.PIN_GENERATION_ATTEMPTS) {
					return this._insertProfileRow(++attempt, rowData);
				}
			}

			throw err;
		})
	}
	// covered 😄👍
	genSecurityPin () {
		return  Math.floor(Math.random() * (this.MAX_PIN_VALUE - this.MIN_PIN_VALUE + 1)) + this.MIN_PIN_VALUE;
	}
	// covered 😄👍
	create (userID, user, profileData) {
		return co(function* () {
            try {
                if (!Number.isInteger(userID)) {
                    return Promise.reject({ validation: 'User ID should be an integer' });
                }

                if(_.isEmpty(profileData)) {
                    throw { validation: 'Empty form data' };
                }

                let prepared = this._validate(profileData);

                const {
                    memberData,
                    USAVValidationError,
                    AAUValidationError
                } = yield this.ProfileSanctioningService.collectSanctioningsData(prepared, user);

                prepared = memberData;

                // delete temporary flags
                prepared = _.omit(prepared, 'is_staff_only', 'sanctioned_by_usav', 'sanctioned_by_aau');

                prepared.shirt_gender 	            = user.gender;
                prepared.shoe_gender 	            = user.gender;
                prepared.user_id 		            = userID;
                prepared.profile_completed_at       = prepared.usav_num ? moment().format() : null;
                prepared.aau_profile_completed_at   = prepared.aau_number ? moment().format() : null;

                let officialsProfile = yield (this._insertProfileRow(this.MIN_ATTEMPT_VALUE, _.omit(prepared, ['clothes'])));

                if (_.isEmpty(officialsProfile)) {
                    throw { validation: 'Profile already exists for the User' };
                }

                if(!_.isEmpty(prepared.clothes)) {
                    const clothes = this.addOfficialIDToClothingSizesArr(prepared.clothes, officialsProfile.official_id);

                    yield this.insertClothesRows(clothes, officialsProfile.official_id);
                }

                return {
                    officialId: officialsProfile.official_id,
                    usavValidationError: USAVValidationError,
                    aauValidationError: AAUValidationError,
                };
            } catch (e) {
                const { err, reason, officialID } = e;

                if (reason === INSERT_CLOTHES_FAILED_REASON) {
                    yield this.removeProfileRow(officialID);
                }

                throw err || e;
            }
		}.bind(this));
	}

    // covered 😄👍
	update (officialID, user, profileData) {
        return co(function* () {
            let tr = null;

            try {
                let officialData = _.pick(profileData, this.PROFILE_FIELDS_FOR_UPDATE);

                if(_.isEmpty(officialData)) {
                    throw { validation: 'Empty form data' };
                }

                officialData = this._validate(officialData);

                // delete fields if updating staff only profile
                if(officialData.is_staff_only) {
                    delete officialData.advancement;
                    delete officialData.rank;
                }

                // set profile_completed_at = NOW() if it was empty
                if(officialData.not_completed) {
                    delete officialData.not_completed;
                    officialData.profile_completed_at = moment().format();
                } else {
                    delete officialData.not_completed;
                }

                if(officialData.aau_not_completed) {
                    delete officialData.aau_not_completed;
                    officialData.aau_profile_completed_at = moment().format();
                } else {
                    delete officialData.aau_not_completed;
                }

                // delete temporary flags
                officialData = _.omit(officialData, 'is_staff_only', 'sanctioned_by_usav', 'sanctioned_by_aau');

                const {
                    memberData,
                    USAVValidationError,
                    AAUValidationError
                } = yield this.ProfileSanctioningService.collectSanctioningsData(officialData, user, officialID);

                officialData = memberData;

                tr = yield Db.begin();

                const updatedProfileId = yield this.updateProfileRow(tr, officialData, officialID);

                if (!_.isEmpty(profileData.clothes)) {
                    const _updateClothingRow = this.updateClothingRow.bind(null, tr, officialID);

                    const rowsCounts = yield Promise.all(profileData.clothes.map(_updateClothingRow))
                        .catch(err => {
                            if (!tr.isCommited) {
                                tr.rollback();
                                throw err;
                            }
                        });

                    const needToInsert = rowsCounts.some(count => count === 0);

                    if (needToInsert) {
                        const clothes = this.addOfficialIDToClothingSizesArr(
                            this.getClothesRowsToInsert(rowsCounts, profileData.clothes),
                            officialID
                        );

                        yield this.insertClothesRows(clothes, officialID,  tr);
                    }
                }

                yield tr.commit();

                return {
                    officialId: updatedProfileId,
                    usavValidationError: USAVValidationError,
                    aauValidationError: AAUValidationError,
                };
            } catch (err) {
                if (tr && !tr.isCommited) {
                    tr.rollback();
                }

                throw err;
            }
        }.bind(this));
    }

    updateProfileRow (tr, officialData, officialID) {
	    const dbClient = tr || Db;

        let query = squel.update().table('official')
            .setFields(officialData)
            .where('official_id = ?', officialID)
            .returning('official_id');

        return dbClient.query(query)
            .then(result => result.rows[0] && result.rows[0].official_id)
            .catch((err) => {
                if (tr && !tr.isCommited) {
                    tr.rollback();
                }

                throw err;
            })
    }

    getClothes(userID) {
        const query = squel.select()
            .from('common_item', 'ci')
            .field('ci.common_item_id')
            .field('ci.title')
            .field(`ci.details->>'size_type'`, 'size_type')
            .field('null as size')
            .left_join('user', 'u',
                squel.expr()
                    .and('u.user_id = ?', userID)
            )
            .where(`ci.details::jsonb->>'gender' IN ('any', u.gender::TEXT)`)
            .where(`ci.item_type = 'event_clothes'`)
            .order(`ci.details::jsonb->>'order'`);

        return Db.query(query)
            .then(({ rows }) => rows);
    }

    insertClothesRows(clothesRows, officialID, tr) {
	    const DbClient = tr || Db;

        const sanitizedRows = clothesRows.map(row => ({
            common_item_id: row.common_item_id,
            size: row.size,
            official_id: row.official_id,
        }));

        const query = squel
            .insert()
            .into('official_clothes_size')
            .setFieldsRows(sanitizedRows);

        return DbClient.query(query)
            .catch(err => {
                if (tr && !tr.isCommited) {
                    tr.rollback();
                }

                throw {
                    err,
                    officialID,
                    reason: 'INSERT_CLOTHES_FAILED',
                };
            })
    }

    updateClothingRow(tr, officialID, row) {
	    const { size, common_item_id } = row;

	    const query = squel
            .update()
            .table('official_clothes_size')
            .set('size', size)
            .where('official_id = ?', officialID)
            .where('common_item_id = ?', common_item_id);

	    return tr.query(query)
            .then(({ rowCount }) => rowCount)
            .catch(err => {
                if (!tr.isCommited) {
                    tr.rollback();
                }

                throw err;
            })
    }

    addOfficialIDToClothingSizesArr(clothes, officialID) {
	    const _clothes = clothes.slice();

	    return _clothes.map(clothing => {
	        clothing.official_id = officialID;
	        return clothing;
        })
    }

    removeProfileRow(officialID) {
	    const query = squel
            .delete()
            .from('official')
            .where('official_id = ?', officialID);

	    return Db.query(query)
            .catch(err => {
                throw err;
            })
    }

    /**
     * This function make filter by rowCount;
     *
     * rowCounts.length === clothingRows.length // always true
     *
     * @param rowCounts     - array of rowCounts after update (values of rowCount for each clothing row)
     * @param clothingRows  - array of clothing rows
     * @returns {Array}     - clothing rows that need to insert (rowCount === 0)
     */
    getClothesRowsToInsert(rowCounts, clothingRows) {
	    return clothingRows.filter((row, index) => {
	        return rowCounts[index] === 0;
        }, rowCounts)
    }

    async updateWebpointInfo (officialID, user) {
        let profileData = await Db.query(
            squel.select().from('official', 'o')
                .field('o.usav_num')
                .field('o.region')
                .field('o.birthdate')
                .where('o.official_id = ?', officialID)
        ).then(result => result.rows && result.rows[0] || null);

        const {
            memberData,
            validationError
        } = await this.ProfileSanctioningService.collectSanctioningData(profileData, user, USAV_SANCTIONING_TYPE, officialID);

        if(validationError) {
            throw validationError;
        }

        return this.updateProfileRow(null, _.omit(memberData, ['usav_num', 'region', 'birthdate']), officialID);
    }

    updateOfficialPaymentInfo(tr, data, officialId) {
        const updateData = this._prepareOfficialPaymentInfoData(data);

        if(!_.isEmpty(updateData)) {
            return this._updateOfficialPaymentInfoRow(tr, updateData, officialId);
        }
    }

    _prepareOfficialPaymentInfoData(data) {
        return Object.keys(data).reduce(
            (result, fieldName) => {
                if (data[fieldName]) {
                    result[fieldName] = knex.raw(`COALESCE(NULLIF(${fieldName}, ''), ?)`, data[fieldName])
                }

                return result;
            }, {}
        );
    }

    _updateOfficialPaymentInfoRow(tr, updateData, officialId) {
        const DbClient = tr || Db;

        const query = knex('official')
            .update(updateData)
            .where('official_id', officialId);

        return DbClient.query(query)
            .catch(err => {
                throw err;
            })
    }
}

module.exports = new ProfileService(ProfileSanctioningService, SportEngineUtils, AAUUtils);
