angular.module('SportWrench').directive('userReg', ['userService', '$http', 'toastr', '$state', '$q', 'geoService',
    'ZIP_REG_EXP_CANADA', 'ZIP_REG_EXP_US', 'ZIP_REG_EXP_CO', '$rootScope', 'SET_USER_NAME', 'COUNTRY_CODES',
    'ZIP_REG_EXP_TT',
    function (
        userService, $http, toastr, $state, $q, geoService, ZIP_REG_EXP_CANADA, ZIP_REG_EXP_US, ZIP_REG_EXP_CO, $rootScope,
        SET_USER_NAME, COUNTRY_CODES, ZIP_REG_EXP_TT
    ) {
    let DEFAULT_REG = {
        country             : 'US',
        gender              : 'male',
        role_staff          : false,
        role_club_director  : false,
        role_sponsor        : false
    }, PSWD_NOT_MATCH = 'Passwords do not match';

    return {
        restrict: 'E',
        scope: {
            mode            : '@',
            showContainer   : '@',
            redirectState   : '@state'
        },
        templateUrl: 'user/register.html',
        link: function (scope) {
            scope.regData = {};
            scope.utils = {
                submitted: false,
                success: false
            };

            scope.geo_data = {
                countries: []
            };

            _loadData();

            if(scope.mode === 'create') {
                scope.regData = _.clone(DEFAULT_REG)
            } else {
                userService.getUser(function (user) {
                    scope.regData = user;
                    scope.utils.username = (user.first + ' ' + user.last);
                    scope.utils.role_event_owner = user.role_event_owner && !user.eo_request;
                });
            }

            scope.disableEOCtrl = function () {
                return (scope.utils.role_event_owner || scope.regData.eo_request);
            };

            scope.getEvaluationValue = function () {
                return (this.mode === 'create')
                            ?this.regData.password
                            :this.regData.new_password
            }

            scope.isCreateMode = function () {
                return (this.mode === 'create')
            }

            scope.getFormTitle = function () {
                return (this.mode === 'create')
                            ?'New Registration'
                            :('Edit user: ' + this.utils.username)
            }

            scope.submitBtnText = function () {
                return (this.mode === 'create')
                        ?'Register':'Save Changes'
            }

            function _loadData() {
                geoService.getCountries().then(function (data) {
                    scope.geo_data.countries = data;
                });
            }

            function __updateUser (data) {
                const updateData = _.omit(
                    data, 'recognition_verification_status', 'allow_login_as_cd'
                );

                userService.updateUser(updateData).then(function (result) {
                    toastr.success('Your profile successfully updated');

                    if(!_.isEmpty(result)) {
                        $rootScope.$emit(SET_USER_NAME, { name: result.first });
                    }

                    $state.reload();
                });
            }

            function __createUser (data) {
                var d = $q.defer();
                $http.post('/api/signup', data).success(function () {
                    scope.utils.success = true;
                    d.resolve()
                }).error(function (error) {
                    d.reject(error)
                });
                return d.promise
            }

            scope.submit = function () {
                this.utils.submitted = true;

                if(this.regForm.$invalid) {
                    toastr.warning('Incorrect data entered. Please, check values and try again')
                    return;
                }

                if(this.mode === 'create') {
                    if(this.regData.password_confirmation !== this.regData.password) {
                        toastr.warning(PSWD_NOT_MATCH);
                        return;
                    }
                } else {
                    if(this.regData.new_password !== this.regData.password_confirmation) {
                        toastr.warning('New ' + PSWD_NOT_MATCH);
                        return;
                    }
                }

                var userData = _.omit(scope.regData, 'role_spectator', 'eo_request');

                if (this.mode === 'create') {
                    __createUser(userData);
                } else {
                    __updateUser(userData);
                }
            };

            scope.getZipCodePattern = function() {
                const ZIP_PATTERNS = {
                    [COUNTRY_CODES.CANADA]: ZIP_REG_EXP_CANADA,
                    [COUNTRY_CODES.COLOMBIA]: ZIP_REG_EXP_CO,
                    [COUNTRY_CODES.TRINIDAD_AND_TOBAGO]: ZIP_REG_EXP_TT
                };

                return ZIP_PATTERNS[scope.regData.country] || ZIP_REG_EXP_US;
            }

            $scope.getPhoneMask = function() {
                const PHONE_MASKS = {
                    [COUNTRY_CODES.HONDURAS]: '9999 9999',
                    [COUNTRY_CODES.POLAND]: '999 999 999',
                    [COUNTRY_CODES.TRINIDAD_AND_TOBAGO]: '9999 9999',
                };

                return PHONE_MASKS[$scope.regData.country] || '(*************';
            };
        }
    }
}]);
