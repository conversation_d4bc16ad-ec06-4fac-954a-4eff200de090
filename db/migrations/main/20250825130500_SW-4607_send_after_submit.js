
exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE public.custom_form_event
            ADD COLUMN IF NOT EXISTS send_after_submit BOOLEAN DEFAULT FALSE NOT NULL;
        COMMENT ON COLUMN public.custom_form_event.send_after_submit IS 'If true, send an email to recipients after the form is submitted.';
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE public.custom_form_event
            DROP COLUMN IF EXISTS send_after_submit;
    `);
};

