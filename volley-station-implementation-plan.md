# VolleyStation Integration Implementation Plan

## Executive Summary

This document outlines the implementation plan for integrating VolleyStation with the SportWrench platform. The integration will provide API endpoints for VolleyStation to retrieve event data (schedule, athletes, staff) and submit results data, following the established SailsJS Actions pattern used in the v2 API controllers.

## Codebase Analysis Results

### Controller Architecture Patterns

**Legacy Pattern (API directory):**
- Location: `api/controllers/API/`
- Example: `TPCController.js` - Traditional SailsJS controller functions
- Used for older integrations like TPC, NCSA, SWB

**Modern Pattern (v2 directory):**
- Location: `api/controllers/v2/API/`
- Example: `baller-tv/events.js` - SailsJS Actions pattern
- **REQUIRED for all new implementations per core engineer mandate**

### Existing Third-Party Integration Patterns

1. **BallerTV Integration** (`api/controllers/v2/API/baller-tv/`)
   - Uses SailsJS Actions pattern
   - Simple API key authentication
   - Event filtering with `teams_settings->>'baller_tv_available'`

2. **ACS Integration** (`api/controllers/v2/API/acs/`)
   - Complex authentication with custom policies
   - Multiple endpoints for different data types

3. **TPC Integration** (`api/controllers/API/TPCController.js`)
   - Legacy pattern but provides reference data structure
   - Uses `UAExportService.getSchedule()` for data retrieval

### Authentication Patterns

**Simple API Key Pattern (Recommended for VolleyStation):**
```javascript
// Policy: api/policies/volley-station/auth.js
if(authorization === sails.config.volleyStation.apiKey) {
    next();
} else {
    res.status(401).json({ error: 'Authorization token invalid' });
}
```

**Configuration Pattern:**
```javascript
// config/volleyStation.js
module.exports.volleyStation = {
    apiKey: 'vs_api_key_to_be_configured'
}
```

## Recommended Architecture

### Directory Structure
```
api/controllers/v2/API/volley-station/
├── events.js           # GET - List enabled events
├── schedule.js         # GET - Event schedule data
├── athletes.js         # GET - Event athletes data  
├── staff.js           # GET - Event staff data
└── results.js         # POST - Submit results data

api/policies/volley-station/
└── auth.js            # Authentication policy

userconfig/routes/api/
└── volley-station.js  # Route definitions

config/
└── volleyStation.js   # Configuration file
```

### Endpoint Design

**Base URL Pattern:** `/api/volley-station/v1/`

1. **GET /api/volley-station/v1/events**
   - Returns events with `volley_station_enabled: true`
   - Similar to BallerTV events endpoint

2. **GET /api/volley-station/v1/events/:eventId/schedule**
   - Returns schedule data using `UAExportService.getSchedule()`
   - Includes matches, teams, courts, timing

3. **GET /api/volley-station/v1/events/:eventId/athletes**
   - Returns athlete roster data
   - Filtered by event and team status

4. **GET /api/volley-station/v1/events/:eventId/staff**
   - Returns staff/officials data
   - Includes roles and assignments

5. **POST /api/volley-station/v1/events/:eventId/results**
   - Accepts match results and scores
   - Validates and stores result data

### Authorization Implementation

**Event-Level Authorization:**
- Check `volley_station_enabled: true` in `event.teams_settings` JSON column
- Pattern: `whereRaw(\`(e.teams_settings->>'volley_station_enabled')::BOOLEAN IS TRUE\`)`
- Return 403 Forbidden if not enabled

**API Key Authentication:**
- Header: `Authorization: vs_api_key_value`
- Validated against `sails.config.volleyStation.apiKey`
- Applied via policy to all VolleyStation endpoints

## Implementation Steps

### Phase 1: Foundation Setup
1. Create configuration file `config/volleyStation.js`
2. Create authentication policy `api/policies/volley-station/auth.js`
3. Create route definitions `userconfig/routes/api/volley-station.js`
4. Create base controller directory structure

### Phase 2: Data Retrieval Endpoints
1. Implement events listing endpoint
2. Implement schedule endpoint (leverage existing `UAExportService`)
3. Implement athletes endpoint
4. Implement staff endpoint
5. Add proper input validation and error handling

### Phase 3: Results Submission
1. Design results data schema
2. Implement results submission endpoint
3. Add data validation and storage logic
4. Implement proper error responses

### Phase 4: Testing & Documentation
1. Create comprehensive API tests
2. Document endpoint specifications
3. Test authorization scenarios
4. Performance testing with large datasets

## Data Structure Reference

The existing TPC integration provides ~75% of the required data structure through `UAExportService.getSchedule()`. Key fields include:

- `match_uuid`, `event`, `match_id`
- `date_time`, `court`, `court_alpha`
- `team_1_name`, `team_2_name`, `ref_name`
- `master_team_id_1`, `master_team_id_2`
- `results` (JSON object with set scores)

Additional athlete and staff data will need separate service methods following similar patterns.

## Configuration Management

**Development/Staging:**
```javascript
// config/volleyStation.js
module.exports.volleyStation = {
    apiKey: 'vs_dev_api_key_12345'
}
```

**Production:**
- API key managed through environment configuration
- Loaded via `config/env/production.js` pattern
- Stored securely in deployment configuration

## Security Considerations

1. **API Key Security:** Store in environment variables, not in code
2. **Rate Limiting:** Consider implementing if needed for high-volume usage
3. **Input Validation:** Strict validation on all POST endpoints
4. **Event Authorization:** Always verify event access permissions
5. **Audit Logging:** Log all API access for monitoring

## Next Steps

1. Review and approve this implementation plan
2. Create detailed API specification document
3. Begin Phase 1 implementation
4. Coordinate with VolleyStation team on data requirements
5. Plan testing and deployment strategy

---

*This plan follows established SportWrench patterns and ensures consistency with existing third-party integrations while meeting the SailsJS Actions requirement for new code.*
