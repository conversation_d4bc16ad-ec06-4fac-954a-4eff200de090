<div style="font-family: Arial, Helvetica, sans-serif; color: #111; font-size: 14px; line-height: 1.4;">
  <p style="margin: 0 0 12px 0;">Hi <%= userName %>,</p>
  <p style="margin: 0 0 12px 0;">Thank you for completing the form.</p>

  <p style="margin: 0 0 6px 0;">
    You submitted the <strong><%= formName %></strong> form for the <strong><%= eventName %></strong> event.
  </p>
  <p style="margin: 0 0 16px 0;">Below are your responses to the form questions.</p>
  <% if (submittedAtEventTZ) { %>
    <p style="margin: 0 0 20px 0;">
      <strong>Submission date:</strong>
      <%= submittedAtEventTZ %>
    </p>
  <% } %>

  <h3 style="margin: 0 0 8px 0; font-size: 16px;">Answers</h3>

  <table style="border-collapse: collapse; width: 100%; font-size: 14px;">
    <thead>
      <tr>
        <th style="text-align: left; background: #f2f2f2; padding: 8px; border: 1px solid #ddd;">Question</th>
        <th style="text-align: left; background: #f2f2f2; padding: 8px; border: 1px solid #ddd;">Answer</th>
      </tr>
    </thead>
    <tbody>
    <% (fields || []).forEach(function(item, idx){ %>
      <tr style="background: <%= (idx % 2 === 0) ? '#ffffff' : '#fbfbfb' %>;">
        <td style="padding: 8px; border: 1px solid #eee; vertical-align: top; width: 45%;"><%= item.label %></td>
        <td style="padding: 8px; border: 1px solid #eee; vertical-align: top; width: 55%;">
          <% if (item && item.type === 'multiselect' && Array.isArray(item.value)) { %>
            <%= item.value.map(String).join(', ') %>
          <% } else { %>
            <%= item.value %>
          <% } %>
        </td>
      </tr>
    <% }); %>
    </tbody>
  </table>
</div>

