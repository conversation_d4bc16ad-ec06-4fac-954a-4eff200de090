
const validationSchema = require('../../../validation-schemas/custom-form');
const FieldsEditingService = require('./fields-editing/_CustomFormFieldsEditingService');

class FormService {

    UNIQUE_TEAM_ASSIGN_FORM_TYPE_CONSTRAINT = 'custom_form_event_team_assign_for_event_type_unique';

    fields = FieldsEditingService

    getData (eventID, formID){
        return this.#getEventCustomFormRow(eventID, formID);
    }

    async update (eventID, formID, newData) {
        this.#validateCustomFormData(newData);

        const currentData = await this.#getCurrentFormData(eventID, formID);

        this.#prepareDataForUpdating(currentData, newData);

        await this.#updateEventCustomFormRow(newData, formID);
    }

    async create (eventID, data) {
        this.#validateCustomFormData(data);
        this.#prepareDataForCreation(eventID, data);

        let formID = await this.#createEventCustomFormRow(data);

        return this.#getEventCustomFormRow(eventID, formID);
    }

    async delete (eventID, formID) {
        if(!eventID) {
            throw { validation: 'Event ID required' }
        }

        if(!formID) {
            throw { validation: 'Form ID required' }
        }

        const currentForm = await this.#getCurrentFormData(eventID, formID);

        if(!currentForm.editable) {
            throw { validation: `Form can't be deleted` };
        }

        let tr;

        try {
            tr = await Db.begin();

            await this.#removeFormRow(tr, eventID, formID);
            await this.#removeFormFields(tr,formID);

            await tr.commit();
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }

    async #removeFormRow (tr, eventID, formID) {
        const query = knex('custom_form_event')
            .delete()
            .where({
                event_id: eventID,
                custom_form_event_id: formID
            });

        const { rowCount } = await tr.query(query);

        if(!rowCount) {
            throw { validation: 'Form not found!' };
        }
    }

    async #removeFormFields (tr, formID) {
        const query = knex('custom_form_field')
            .delete()
            .where('custom_form_event_id', formID);

        return tr.query(query);
    }

    #validateCustomFormData (formData) {
        let validationResult = validationSchema.customFormData.validate(formData);

        if (validationResult.error) {
            throw { validationErrors: validationResult.error.details };
        }
    }

    #prepareDataForCreation (eventID, data) {
        if(data.published) {
            data.published = knex.fn.now();
        } else {
            data.published = null;
        }

        data.event_id = eventID;
    }

    async #createEventCustomFormRow (data) {
        let query = knex('custom_form_event as cfe')
            .insert(data)
            .returning('cfe.custom_form_event_id AS id');

        try {
            const { rows: [form] } = await Db.query(query);

            if(_.isEmpty(form)) {
                throw { validation: 'Custom Form not created' };
            }

            return form.id;
        } catch (err) {
            if(Db.utils.isUniqueConstraintViolation(err, this.UNIQUE_TEAM_ASSIGN_FORM_TYPE_CONSTRAINT)) {
                throw { validation: '"Team Assign for event" placement could be used by one form per event' };
            }

            throw err;
        }
    }

    async #getCurrentFormData (eventID, formID) {
        let currentData = await this.#getEventCustomFormRow(eventID, formID);

        if(_.isEmpty(currentData)) {
            throw { validation: 'Custom Form not found' };
        }

        return currentData;
    }

    #prepareDataForUpdating (currentData, newData) {
        if(currentData.published !== newData) {
            if(newData.published) {
                newData.published = knex.fn.now();
            } else {
                newData.published = null;
            }
        }
    }

    async #updateEventCustomFormRow (newData, formID) {
        let query = knex('custom_form_event as cfe')
            .update(newData)
            .where('cfe.custom_form_event_id', formID)

        try {
            const { rowCount } = await Db.query(query);

            if(!rowCount) {
                throw { validation: 'Custom Form not found' };
            }
        } catch (err) {
            if(Db.utils.isUniqueConstraintViolation(err, this.UNIQUE_TEAM_ASSIGN_FORM_TYPE_CONSTRAINT)) {
                throw { validation: '"Team Assign for event" placement could be used by one form per event' };
            }

            throw err;
        }
    }

    async #getEventCustomFormRow (eventID, formID) {
        let query = knex('custom_form_event as cfe')
            .select({
                name: 'cfe.name',
                custom_form_event_id: 'cfe.custom_form_event_id',
                published: knex.raw(`cfe.published IS NOT NULL`),
                type: 'cfe.type',
                header_text: 'cfe.header_text',
                send_after_submit: 'cfe.send_after_submit',
                editable: knex.raw(`(
                    SELECT COUNT(*) = 0
                    FROM custom_form_submitted_field_value cfsfv
                    WHERE cfe.custom_form_event_id = cfsfv.custom_form_event_id
                )`),
                fields: knex.raw(`
                    COALESCE(ARRAY_TO_JSON(ARRAY_AGG(json_build_object(
                       'type', cfft.type,
                       'options', cff.options,
                       'default_value', cff.default_value,
                       'is_required', cff.is_required,
                       'label', cff.label,
                       'id', cff.custom_form_field_id,
                       'variation', cff.variation,
                       'help_text', cff.help_text,
                       'section', cff.section,
                       'settings', cff.settings,
                       'sort_order', cff.sort_order
                                                ) ORDER BY cff.sort_order)
                                      FILTER ( WHERE cff.custom_form_field_id IS NOT NULL)), '[]'::JSON)
                `)
            })
            .leftJoin('custom_form_field as cff', 'cff.custom_form_event_id', 'cfe.custom_form_event_id')
            .leftJoin('custom_form_field_type as cfft', 'cfft.custom_form_field_type_id', 'cff.custom_form_field_type_id')
            .where('cfe.event_id', eventID)
            .where('cfe.custom_form_event_id', formID)
            .groupBy('cfe.name', 'cfe.custom_form_event_id', 'cfe.published', 'cfe.type', 'cfe.header_text', 'cfe.send_after_submit')

        const { rows: [form] } = await Db.query(query);

        return form;
    }
}

module.exports = new FormService();
