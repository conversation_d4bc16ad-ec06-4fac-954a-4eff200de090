'use strict';

const utils     = require('../../lib/swUtils');
const path      = require('path');
const spawn     = require('child_process').spawn;

const SAILS_CONNECTION = sails.config.connections[sails.config.db.connection];

const customRecipientSchema = require('../../validation-schemas/custom-recipient').custom_recipient;

class CustomRecipients {
    get UNIQUE_EMAIL_IN_LIST_CONSTRAINT_NAME () {
        return 'custom_recipient_email_custom_recipients_list_id_key';
    }

    get importFile() {
        if(!this._importFile) {
            this._importFile = require('../../lib/FileStreamUploadService');
        }
        return this._importFile;
    }

    get S3_FOLDER () {
        return 'customRecipientsImport';
    }

    get FORM_ELEMENT_NAME () {
        return 'file';
    }

    get MODE () {
        return {
            CREATE: 'create',
            UPDATE: 'update'
        }
    }

    get VISIBILITY_SCOPE () {
        return {
            EO      : 'eo',
            EVENT   : 'event'
        }
    }

    get IMPORT_OPTION () {
        return {
            DELETE: 'delete',
            UPDATE: 'update'
        }
    }

    get IMPORT_FILENAME () {
        return 'custom_recipients_import.js';
    }

    get IMPORTER_PATH () {
        return path.resolve(__dirname, '..', '..', '..', 'sw-utils');
    }

    get TEMP_PATH () {
        return path.resolve(__dirname, '..', '..', '..', '..', 'uploads');
    }

    getAllLists (eventID, eventOwnerID) {
        let query = squel.select().from('custom_recipients_list', 'crl')
            .field(`(SELECT COUNT(*) FROM custom_recipient cr 
                        WHERE cr.custom_recipients_list_id=crl.custom_recipients_list_id)`, 'email_count')
            .field('crl.custom_recipients_list_id', 'list_id')
            .field('crl.title')
            .field('crl.event_id IS NULL', 'is_for_all_events')
            .where('crl.event_owner_id = ?', eventOwnerID)
            .where('crl.event_id = ? OR crl.event_id IS NULL', eventID);

        return Db.query(query).then(result => result.rows);
    }

    getRecipientsList (listID, eventOwnerID) {
        let query = squel.select().from('custom_recipients_list', 'crl')
            .field(
                `(SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("t"))), '[]' :: JSON) 
                    FROM (SELECT cr.first, cr.last, cr.email, cr.custom_recipient_id
                    FROM custom_recipient cr
                    WHERE cr.custom_recipients_list_id = crl.custom_recipients_list_id
                    ) t)`
                , 'recipients')
            .field(`(SELECT COUNT(*) FROM custom_recipient cr 
                        WHERE cr.custom_recipients_list_id=crl.custom_recipients_list_id)`, 'email_count')
            .field('crl.title')
            .field('crl.custom_recipients_list_id', 'id')
            .where('crl.event_owner_id = ?', eventOwnerID)
            .where('crl.custom_recipients_list_id = ?', listID);

        return Db.query(query).then(result => result.rows[0] || null);
    }

    async changeContactListTitle (listID, eventOwnerID, title) {
        let query = squel.update().table('custom_recipients_list', 'crl')
            .set('title', title.trim())
            .where('crl.event_owner_id = ?'             , eventOwnerID)
            .where('crl.custom_recipients_list_id = ?'  , listID);

        let { text: sql, values } = query.toParam();

        return Db.query(sql, values);
    }

    async deleteRecipientsList (listID, eventOwnerID) {
        let listIsUsing = await this.__checkContactListIsUsing(listID);

        if(listIsUsing) {
            throw { validation: 'Can\'t delete contact list while it is being used by email sending job' };
        }

        let tr = await Db.begin();

        let deleteListQuery     = squel.delete().from('custom_recipients_list', 'crl')
            .where('crl.custom_recipients_list_id = ?'  , listID)
            .where('crl.event_owner_id = ?'             , eventOwnerID);

        let deleteContactsQuery = squel.delete().from('custom_recipient', 'cr')
            .where('cr.custom_recipients_list_id = ?'   , listID);

        try {
            await Promise.all([
                tr.query(deleteContactsQuery),
                tr.query(deleteListQuery)
            ]);

            return tr.commit();
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }

    async addRecipient (listID, contact) {
        contact = this.__validateContactData(contact);

        let listIsUsing = await this.__checkContactListIsUsing(listID);

        if(listIsUsing) {
            throw { validation: 'Can\'t add contact to list while it is being used by email sending job' };
        }

        contact.custom_recipients_list_id = listID;

        let query = squel.insert().into('custom_recipient')
            .set(`document_row`, '[]', { dontQuote: true })
            .setFields(contact)
            .returning('first, last, custom_recipients_list_id, email, custom_recipient_id');

        let { text: sql, values } = query.toParam();

        return Db.query(sql, values)
            .then(result => result.rows[0] || null)
            .catch(this.__catchUniqEmailConstraintError.bind(this));
    }

    async updateContact (listID, contactID, contact) {
        let listIsUsing = await this.__checkContactListIsUsing(listID);

        if(listIsUsing) {
            throw { validation: 'Can\'t update contact while it is being used by email sending job' };
        }

        contact = this.__validateContactData(contact);

        let query = squel.update().table('custom_recipient', 'cr')
            .setFields(contact)
            .where('cr.custom_recipients_list_id = ?'   , listID)
            .where('cr.custom_recipient_id = ?'         , contactID);

        let { text: sql, values } = query.toParam();

        return Db.query(sql, values)
            .then(result => result.rowCount > 0)
            .catch(this.__catchUniqEmailConstraintError.bind(this));
    }

    async deleteRecipient (listID, contactID) {
        let listIsUsing = await this.__checkContactListIsUsing(listID);

        if(listIsUsing) {
            throw { validation: 'Can\'t delete contact while it is being used by email sending job' };
        }

        let query = squel.remove().from('custom_recipient', 'cr')
            .where('cr.custom_recipients_list_id = ?'   , listID)
            .where('cr.custom_recipient_id = ?'         , contactID);

        return Db.query(query).then(result => result.rowCount > 0);
    }

    async validateListParams ({eventID, title, visibility_scope, files, import_option, listID}, mode) {
        if(files.length > 1) {
            return 'Multiple files in request';
        }

        if(!title) {
            return 'Contacts list title required';
        }

        if(mode === this.MODE.CREATE) {
            if(
                !visibility_scope || ![this.VISIBILITY_SCOPE.EO, this.VISIBILITY_SCOPE.EVENT].includes(visibility_scope)
            ) {
                return 'Contacts list visibility scope required';
            }
        } else {
            if(!import_option || ![this.IMPORT_OPTION.DELETE, this.IMPORT_OPTION.UPDATE].includes(import_option)) {
                return 'Invalid import option value';
            }

            if(!listID) {
                return 'Invalid list ID';
            }

            let contactListIsInUse = await this.__checkContactListIsUsing(listID);

            if(contactListIsInUse) {
                return `Can't delete contact list while it is being used by email sending job`;
            }
        }

        if(!eventID) {
            return 'Event ID required';
        }
    }

    /**
     *
     * @param {Upstream} originStream   - binary file stream
     * @param {Function} fileName       - function for filename generation
     * @param {Number} eventID          - null if "visibility scope" = "all"
     * @param {Number} eventOwnerID
     * @param {Number} userID
     * @param {String} title
     * @param {Number} listID
     * @param {String} import_option
     * @returns {Promise<void>}
     */
    async importContacts (
        originStream, fileName, { eventID = null, eventOwnerID, userID, title, listID = null, import_option = null }
    ) {
        let serverFilePath, S3FilePath, importFile;

        try {
            let importFile = await this.importFile.uploadFile(originStream, fileName, this.S3_FOLDER);

            S3FilePath     = importFile.s3FilePath;
            serverFilePath = importFile.serverFilePath;

            let importResult = await this.__saveContacts(
                serverFilePath, eventID, eventOwnerID, userID, title, S3FilePath, listID, import_option
            );

            return importResult;
        } finally {
            if(importFile) {
                importFile.removeServerFile().catch(err => loggers.errors_log.error(err));
            }
        }
    }

    __saveContacts (filePath, eventID, eoID, userID, title, s3Path, listID = null, import_option = null) {
        return this.__parseAndSaveRecipientsList(filePath, eventID, eoID, userID, title, s3Path, listID, import_option);
    }

    __parseAndSaveRecipientsList (filePath, eventID, eoID, userID, title, s3Path, listID = null, import_option = null) {
        return new Promise((resolve, reject) => {
            let result, error;

            let procParams = [
                this.IMPORT_FILENAME,
                `--event=${eventID}`,
                `--owner=${eoID}`,
                `--user=${userID}`,
                `--title=${title}`,
                `--s3path=${s3Path}`,
                `--path=${filePath}`,
                `--conn=${Buffer.from(JSON.stringify(SAILS_CONNECTION)).toString('base64')}`,
            ];

            if(listID) {
                procParams = procParams.concat([
                    `--list_id=${listID}`,
                    `--import_option=${import_option}`,
                    `--mode=${this.MODE.UPDATE}`
                ]);
            } else {
                procParams = procParams.concat([`--mode=${this.MODE.CREATE}`]);
            }

            let proc = spawn('node', procParams, {
                cwd     : this.IMPORTER_PATH,
                stdio   : 'pipe'
            });

            proc.on('error', (err) => reject(err));

            proc.on('close', (code) => {
                if(code > 0) {
                    reject({validation: this.__getStdoutString(error)});
                } else {
                    try {
                        resolve(JSON.parse(this.__getStdoutString(result)));
                    } catch (err) {
                        reject(err);
                    }
                }
            });

            proc.stdout.on('data', (data) => result = data);
            proc.stderr.on('data', (err) => error = err);
            proc.stdout.on('end', () => {});
        })
    }

    __getStdoutString (buffer) {
        return Buffer.from(buffer, 'base64').toString('utf-8');
    }

    __checkContactListIsUsing (listID) {
        let query = squel.select().from('system_job', 'sj')
            .field('1')
            .where('sj.custom_recipients_list_id = ?', listID)
            .where('sj.job_type = \'email_sending\'')
            .where('sj.status <> \'done\'')
            .limit(1);

        return Db.query(query).then(result => result.rows && result.rows.length === 1);
    }

    __validateContactData (contact) {
        let validationResult = customRecipientSchema.validate(contact);

        if (validationResult.error) {
            throw { validationErrors: validationResult.error.details };
        }

        return validationResult.value;
    }

    __catchUniqEmailConstraintError (err) {
        if(err.constraint && err.constraint === this.UNIQUE_EMAIL_IN_LIST_CONSTRAINT_NAME) {
            throw { validation: 'Contact with corresponding email exists' };
        }

        throw err;
    }
}

module.exports = new CustomRecipients();
