/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
    return knex.raw(`
        INSERT INTO "public"."country" (code, name, calling_code) VALUES ('TT', 'Trinidad and Tobago', 868);
        INSERT INTO "public"."region" (region, name, country) VALUES ('TT', 'Trinidad and Tobago', 'TT');
    `);
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
    return knex.raw(`
        DELETE FROM "public"."country" WHERE name = 'Trinidad and Tobago';
        DELETE FROM "public"."region" WHERE name = 'Trinidad and Tobago';
    `);
};
