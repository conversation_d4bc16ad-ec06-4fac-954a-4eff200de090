# VolleyStation Integration Implementation Plan

## Executive Summary

This document outlines the implementation plan for integrating VolleyStation with the SportWrench platform. The integration will provide API endpoints for VolleyStation to retrieve event data (schedule, athletes, staff) and submit results data, following the established **SailsJS Actions** pattern used in the v2 API controllers.

## Codebase Analysis Results

### Controller Architecture Patterns

**Legacy Pattern (API directory):**
- Location: `api/controllers/API/`
- Example: `TPCController.js` — traditional SailsJS controller functions
- Used for older integrations like TPC, NCSA, SWB

**Modern Pattern (v2 directory):**
- Location: `api/controllers/v2/API/`
- Example: `baller-tv/events.js` — uses SailsJS Actions
- **Required for all new implementations**

### Existing Third-Party Integration Patterns (examples)

1. **BallerTV Integration** (`api/controllers/v2/API/baller-tv/`)
    - Uses SailsJS Actions pattern
    - Simple API key authentication
    - Event filtering with `teams_settings->>'baller_tv_available'`

2. **ACS Integration** (`api/controllers/v2/API/acs/`)
    - Complex authentication with custom policies
    - Multiple endpoints for different data types

3. **TPC Integration** (`api/controllers/API/TPCController.js`)
    - Legacy pattern but provides reference for data structures
    - Uses `UAExportService.getSchedule()` for schedule data retrieval

## Architecture Plan

### Directory Structure
```
api/controllers/v2/API/volley-station/
├── schedule.js     # GET - Event schedule data
├── athletes.js     # GET - Event athletes data
├── staff.js        # GET - Event staff data
└── results.js      # POST - Submit results data

api/policies/volley-station/
└── auth.js         # Authentication policy

userconfig/routes/api/
└── volley-station.js  # Route definitions

config/
└── volleyStation.js   # Configuration file
```

### Endpoint Design

**Base URL Pattern:** `/api/volley-station/v1/`

1. **GET `/api/volley-station/v1/events/:eventId/schedule`**
    - Returns schedule data (matches, teams, courts, timing)
    - Leverages existing `UAExportService.getSchedule()`

2. **GET `/api/volley-station/v1/events/:eventId/athletes`**
    - Returns athlete roster data, filtered by event and team status

3. **GET `/api/volley-station/v1/events/:eventId/staff`**
    - Returns staff/officials data, including roles and assignments

4. **POST `/api/volley-station/v1/events/:eventId/results`**
    - Accepts match results and scores
    - Validates and persists results data

### Authorization Implementation

**Event-Level Authorization:**
- Condition: `volley_station_enabled: true` in `event.teams_settings` JSON column
- Query pattern:
  ```sql
  (e.teams_settings->>'volley_station_enabled')::BOOLEAN IS TRUE
  ```
- Return **403 Forbidden** if not enabled

**API Key Authentication:**
- Header: `Authorization: vs_api_key_value`
- Validated against `sails.config.volleyStation.apiKey`
- Applied as a policy across all VolleyStation endpoints

## Implementation Steps

### Phase 1: Foundation Setup
1. Create configuration file `config/volleyStation.js`
2. Implement authentication policy `api/policies/volley-station/auth.js`
3. Define routes in `userconfig/routes/api/volley-station.js`
4. Create base controller directory structure

### Phase 2: Data Retrieval Endpoints
1. Implement **events listing** endpoint (if required for discovery)
2. Implement **schedule** endpoint (leveraging `UAExportService`)
3. Implement **athletes** endpoint
4. Implement **staff** endpoint
5. Add strict input validation and error handling

### Phase 3: Results Submission
1. Design and finalize results data schema
2. Implement results submission endpoint
3. Add validation and storage logic
4. Ensure consistent error and success responses

### Phase 4: Testing & Documentation
1. Develop comprehensive API tests (unit + integration)
2. Create OpenAPI/Swagger documentation for endpoints
3. Test different authorization and failure scenarios

## Security Considerations
1. **API Key Management:** Store in environment variables, never in code
2. **Rate Limiting:** Consider applying for high-volume request protection
3. **Monitoring:** Log and audit API access for security and debugging
